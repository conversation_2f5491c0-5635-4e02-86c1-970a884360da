/**
 * API Endpoints Configuration
 *
 * This file centralizes all API endpoint URLs and ensures they're correctly configured
 * with the appropriate base URL from environment variables.
 */

// Get API base URL from environment or use relative URLs
const API_BASE_URL = process.env.REACT_APP_API_URL || '';

// For development, use proxy path when running through webpack dev server
const isDevelopment = process.env.NODE_ENV === 'development';
// In development, use relative URLs to leverage the proxy setup
// In production, use the full API_BASE_URL
const API_PREFIX = isDevelopment ? '/api' : API_BASE_URL;

// Log the API base URL for debugging
console.log('API Base URL:', API_BASE_URL || 'Using relative URLs');
console.log('Development mode:', isDevelopment);
console.log('API Prefix:', API_PREFIX);

// Define all API endpoints with multiple variations to handle different backend configurations
export const API_ENDPOINTS = {
  // CSRF token endpoint
  CSRF_TOKEN: `${API_PREFIX}/csrf-token/`,

  // API status endpoints (try multiple variations)
  STATUS: [
    `${API_PREFIX}/status/`,
    `${API_PREFIX}/health/`,
    `${API_PREFIX}/health-check/`
  ],

  HEALTH: [
    `${API_PREFIX}/health/`,
    `${API_PREFIX}/health-check/`,
    `${API_PREFIX}/status/`
  ],

  // App data endpoints (try multiple variations)
  APP_DATA: [
    `${API_PREFIX}/get_app_data/`,
    `${API_PREFIX}/apps/`,
    `${API_PREFIX}/v1/apps/`,
    `${API_PREFIX}/app-data/`
  ],

  SAVE_APP_DATA: [
    `${API_PREFIX}/save_app_data/`,
    `${API_PREFIX}/apps/`,
    `${API_PREFIX}/v1/apps/`,
    `${API_PREFIX}/app-data/`
  ],

  EXPORT_APP_DATA: [
    `${API_PREFIX}/api/app-data/export/`,
    `${API_PREFIX}/export_app_data/`
  ],

  IMPORT_APP_DATA: [
    `${API_PREFIX}/api/app-data/import/`,
    `${API_PREFIX}/import_app_data/`
  ],

  // AI endpoints
  GENERATE_AI_SUGGESTIONS: [
    `${API_BASE_URL}/api/ai/suggestions/`,
    `${API_BASE_URL}/generate_ai_suggestions/`
  ],

  GENERATE_IMAGE: [
    `${API_BASE_URL}/api/ai/generate-image/`,
    `${API_BASE_URL}/generate_image/`
  ],

  // Authentication endpoints
  LOGIN: [
    `${API_BASE_URL}/api/auth/login/`,
    `${API_BASE_URL}/auth/login/`
  ],

  REGISTER: [
    `${API_BASE_URL}/api/auth/register/`,
    `${API_BASE_URL}/auth/register/`
  ],

  USER_PROFILE: [
    `${API_BASE_URL}/api/auth/profile/`,
    `${API_BASE_URL}/auth/profile/`
  ],

  UPDATE_PROFILE: [
    `${API_BASE_URL}/api/auth/profile/update/`,
    `${API_BASE_URL}/auth/profile/update/`
  ],

  // API keys endpoints
  API_KEYS: [
    `${API_BASE_URL}/api/api-keys/`,
    `${API_BASE_URL}/api/v1/api-keys/`
  ],

  VALIDATE_API_KEY: [
    `${API_BASE_URL}/api/validate-api-key/`,
    `${API_BASE_URL}/api/v1/validate-api-key/`
  ],

  // REST API endpoints
  API_V1: `${API_BASE_URL}/api/v1/`,
  API_V2: `${API_BASE_URL}/api/v2/`,

  // GraphQL endpoint
  GRAPHQL: `${API_BASE_URL}/graphql/`,
};

/**
 * Get the first URL from an endpoint configuration
 * @param {string|Array} endpoint - Endpoint configuration
 * @returns {string} The first URL
 */
export function getFirstEndpointUrl(endpoint) {
  if (Array.isArray(endpoint)) {
    return endpoint[0];
  }
  return endpoint;
}

/**
 * Get all URLs for an endpoint
 * @param {string|Array} endpoint - Endpoint configuration
 * @returns {Array} Array of URLs
 */
export function getAllEndpointUrls(endpoint) {
  if (Array.isArray(endpoint)) {
    return endpoint;
  }
  return [endpoint];
}

export default API_ENDPOINTS;